//
//  UserDataRecoveryTool.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import Foundation
import CoreData
import CloudKit

/**
 * 用户数据恢复工具
 * 提供用户友好的数据恢复和诊断功能
 * 帮助用户在数据丢失时进行恢复
 */
@MainActor
class UserDataRecoveryTool: ObservableObject {
    
    // MARK: - Singleton
    static let shared = UserDataRecoveryTool()
    
    // MARK: - Published Properties
    @Published var isRecovering: Bool = false
    @Published var recoveryProgress: RecoveryProgress = RecoveryProgress()
    @Published var lastRecoveryReport: RecoveryReport?
    @Published var availableBackups: [BackupInfo] = []
    
    // MARK: - Private Properties
    private let multiLayerStorageManager = MultiLayerStorageManager.shared
    private let pointRecordBackupService = PointRecordBackupService.shared
    private let dataConsistencyService = DataConsistencyService.shared
    private let enhancedCloudKitSyncService = EnhancedCloudKitSyncService.shared
    private let coreDataManager = CoreDataManager.shared
    
    // MARK: - Initialization
    private init() {}
    
    // MARK: - Public Methods
    
    /**
     * 执行数据诊断
     */
    func performDataDiagnosis() async -> DiagnosisReport {
        print("🔍 开始数据诊断...")
        
        var report = DiagnosisReport()
        report.diagnosisStartTime = Date()
        
        // 1. 检查数据完整性
        report.dataIntegrityStatus = await checkDataIntegrity()
        
        // 2. 检查备份可用性
        report.backupAvailability = await checkBackupAvailability()
        
        // 3. 检查CloudKit连接
        report.cloudKitStatus = await checkCloudKitConnection()
        
        // 4. 检查数据一致性
        report.consistencyStatus = await checkDataConsistency()
        
        // 5. 生成诊断建议
        report.recommendations = generateDiagnosisRecommendations(report)
        
        report.diagnosisEndTime = Date()
        
        print("✅ 数据诊断完成")
        return report
    }
    
    /**
     * 执行智能数据恢复
     */
    func performIntelligentRecovery() async -> RecoveryReport {
        guard !isRecovering else {
            print("⚠️ 数据恢复正在进行中")
            return RecoveryReport(success: false, message: "数据恢复正在进行中，请稍候")
        }
        
        isRecovering = true
        recoveryProgress = RecoveryProgress()
        
        print("🚀 开始智能数据恢复...")
        
        var report = RecoveryReport()
        report.recoveryStartTime = Date()
        
        do {
            // 步骤1: 数据诊断 (20%)
            recoveryProgress.currentStep = "数据诊断"
            recoveryProgress.progress = 0.2
            let diagnosis = await performDataDiagnosis()
            report.diagnosisReport = diagnosis
            
            // 步骤2: 备份数据检查 (40%)
            recoveryProgress.currentStep = "检查备份数据"
            recoveryProgress.progress = 0.4
            let backupStatus = await checkAndPrepareBackups()
            report.backupStatus = backupStatus
            
            // 步骤3: 执行数据恢复 (70%)
            recoveryProgress.currentStep = "恢复数据"
            recoveryProgress.progress = 0.7
            let recoveryResult = await executeDataRecovery()
            report.recoveryResult = recoveryResult
            
            // 步骤4: 数据验证 (90%)
            recoveryProgress.currentStep = "验证数据"
            recoveryProgress.progress = 0.9
            let validationResult = await validateRecoveredData()
            report.validationResult = validationResult
            
            // 步骤5: 完成恢复 (100%)
            recoveryProgress.currentStep = "完成恢复"
            recoveryProgress.progress = 1.0
            await finalizeRecovery()
            
            report.success = true
            report.message = "数据恢复成功完成"
            
        } catch {
            report.success = false
            report.message = "数据恢复失败: \(error.localizedDescription)"
            print("❌ 数据恢复失败: \(error)")
        }
        
        report.recoveryEndTime = Date()
        lastRecoveryReport = report
        isRecovering = false
        
        print("✅ 智能数据恢复完成")
        return report
    }
    
    /**
     * 从特定备份恢复数据
     */
    func recoverFromBackup(_ backupInfo: BackupInfo) async -> RecoveryReport {
        print("📦 从备份恢复数据: \(backupInfo.source)")
        
        var report = RecoveryReport()
        report.recoveryStartTime = Date()
        
        do {
            switch backupInfo.type {
            case .pointRecords:
                let result = await pointRecordBackupService.restorePointRecords()
                report.success = result.success
                report.message = result.success ? "积分记录恢复成功" : "积分记录恢复失败"
                
            case .multiLayer:
                let result = await multiLayerStorageManager.performDataRecovery()
                report.success = result
                report.message = result ? "多层数据恢复成功" : "多层数据恢复失败"
                
            case .cloudKit:
                let syncResult = await enhancedCloudKitSyncService.forceSync()
                report.success = syncResult.success
                report.message = syncResult.success ? "CloudKit数据恢复成功" : "CloudKit数据恢复失败"
            }
            
        } catch {
            report.success = false
            report.message = "备份恢复失败: \(error.localizedDescription)"
        }
        
        report.recoveryEndTime = Date()
        return report
    }
    
    /**
     * 获取可用备份列表
     */
    func getAvailableBackups() async -> [BackupInfo] {
        var backups: [BackupInfo] = []
        
        // 检查积分记录备份
        let pointBackupIntegrity = await pointRecordBackupService.verifyBackupIntegrity()
        if pointBackupIntegrity.validBackupCount > 0 {
            backups.append(BackupInfo(
                type: .pointRecords,
                source: "积分记录备份",
                timestamp: Date(),
                isValid: pointBackupIntegrity.isFullyValid,
                description: "包含 \(pointRecordBackupService.totalBackupCount) 条积分记录"
            ))
        }
        
        // 检查多层存储备份
        let multiLayerReport = await multiLayerStorageManager.checkDataConsistency()
        if !multiLayerReport.inconsistentAreas.isEmpty {
            backups.append(BackupInfo(
                type: .multiLayer,
                source: "多层存储备份",
                timestamp: multiLayerStorageManager.lastSyncDate ?? Date(),
                isValid: multiLayerReport.isFullyConsistent,
                description: "包含用户、成员和积分数据"
            ))
        }
        
        // 检查CloudKit备份
        let syncStats = enhancedCloudKitSyncService.getSyncStatistics()
        if let lastSync = syncStats.lastSyncDate {
            backups.append(BackupInfo(
                type: .cloudKit,
                source: "CloudKit备份",
                timestamp: lastSync,
                isValid: syncStats.currentSyncStatus != .failed(CloudKitSyncError.unknownError(NSError())),
                description: "iCloud云端数据"
            ))
        }
        
        availableBackups = backups
        return backups
    }
    
    /**
     * 清除所有本地数据（危险操作）
     */
    func clearAllLocalData() async -> Bool {
        print("⚠️ 执行清除所有本地数据操作...")
        
        do {
            // 删除所有Core Data实体
            try await deleteAllCoreDataEntities()
            
            // 清除UserDefaults备份
            clearUserDefaultsBackups()
            
            // 重置同步状态
            enhancedCloudKitSyncService.resetSyncState()
            
            print("✅ 本地数据清除完成")
            return true
            
        } catch {
            print("❌ 清除本地数据失败: \(error)")
            return false
        }
    }
    
    /**
     * 导出数据备份
     */
    func exportDataBackup() async -> URL? {
        print("📤 导出数据备份...")
        
        do {
            let backupData = try await createBackupExport()
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let backupURL = documentsPath.appendingPathComponent("ztt2_backup_\(Date().timeIntervalSince1970).json")
            
            try backupData.write(to: backupURL)
            
            print("✅ 数据备份导出完成: \(backupURL.path)")
            return backupURL
            
        } catch {
            print("❌ 数据备份导出失败: \(error)")
            return nil
        }
    }
    
    /**
     * 从导出文件导入数据
     */
    func importDataBackup(from url: URL) async -> Bool {
        print("📥 从文件导入数据备份...")
        
        do {
            let backupData = try Data(contentsOf: url)
            let success = try await restoreFromBackupData(backupData)
            
            if success {
                print("✅ 数据备份导入成功")
            } else {
                print("❌ 数据备份导入失败")
            }
            
            return success
            
        } catch {
            print("❌ 数据备份导入失败: \(error)")
            return false
        }
    }

    // MARK: - Private Methods

    /**
     * 检查数据完整性
     */
    private func checkDataIntegrity() async -> DataIntegrityStatus {
        let stats = await dataConsistencyService.getDataStatisticsSummary()

        if stats.orphanedMemberCount > 0 || stats.orphanedPointRecordCount > 0 {
            return .corrupted("发现孤立数据")
        } else if stats.totalRecords == 0 {
            return .empty("没有数据")
        } else {
            return .healthy("数据完整")
        }
    }

    /**
     * 检查备份可用性
     */
    private func checkBackupAvailability() async -> BackupAvailabilityStatus {
        let backups = await getAvailableBackups()
        let validBackups = backups.filter { $0.isValid }

        if validBackups.isEmpty {
            return .unavailable("没有可用备份")
        } else if validBackups.count == backups.count {
            return .available("所有备份可用")
        } else {
            return .partial("部分备份可用")
        }
    }

    /**
     * 检查CloudKit连接
     */
    private func checkCloudKitConnection() async -> CloudKitConnectionStatus {
        let syncResult = await enhancedCloudKitSyncService.performIntelligentSync()

        if syncResult.success {
            return .connected("CloudKit连接正常")
        } else {
            return .disconnected("CloudKit连接失败: \(syncResult.error?.localizedDescription ?? "未知错误")")
        }
    }

    /**
     * 检查数据一致性
     */
    private func checkDataConsistency() async -> DataConsistencyStatus {
        let report = await dataConsistencyService.performFullConsistencyCheck()

        if report.totalIssuesFound == 0 {
            return .consistent("数据一致")
        } else {
            return .inconsistent("发现 \(report.totalIssuesFound) 个一致性问题")
        }
    }

    /**
     * 生成诊断建议
     */
    private func generateDiagnosisRecommendations(_ report: DiagnosisReport) -> [String] {
        var recommendations: [String] = []

        switch report.dataIntegrityStatus {
        case .corrupted:
            recommendations.append("建议执行数据修复")
        case .empty:
            recommendations.append("建议从备份恢复数据")
        case .healthy:
            break
        }

        switch report.backupAvailability {
        case .unavailable:
            recommendations.append("建议立即创建数据备份")
        case .partial:
            recommendations.append("建议检查备份完整性")
        case .available:
            break
        }

        switch report.cloudKitStatus {
        case .disconnected:
            recommendations.append("建议检查网络连接和iCloud设置")
        case .connected:
            break
        }

        switch report.consistencyStatus {
        case .inconsistent:
            recommendations.append("建议执行数据一致性修复")
        case .consistent:
            break
        }

        if recommendations.isEmpty {
            recommendations.append("数据状态良好，无需特别处理")
        }

        return recommendations
    }

    /**
     * 检查并准备备份
     */
    private func checkAndPrepareBackups() async -> String {
        let backups = await getAvailableBackups()

        if backups.isEmpty {
            return "没有可用备份"
        } else {
            return "找到 \(backups.count) 个备份源"
        }
    }

    /**
     * 执行数据恢复
     */
    private func executeDataRecovery() async -> String {
        // 1. 尝试多层存储恢复
        let multiLayerResult = await multiLayerStorageManager.performDataRecovery()

        // 2. 尝试积分记录恢复
        let pointResult = await pointRecordBackupService.restorePointRecords()

        // 3. 尝试数据一致性修复
        let consistencyReport = await dataConsistencyService.performFullConsistencyCheck()
        let fixResult = await dataConsistencyService.autoFixInconsistencies(report: consistencyReport)

        var results: [String] = []

        if multiLayerResult {
            results.append("多层存储恢复成功")
        }

        if pointResult.success {
            results.append("积分记录恢复成功")
        }

        if fixResult.getTotalFixedCount() > 0 {
            results.append("修复了 \(fixResult.getTotalFixedCount()) 个数据问题")
        }

        return results.isEmpty ? "没有需要恢复的数据" : results.joined(separator: "；")
    }

    /**
     * 验证恢复的数据
     */
    private func validateRecoveredData() async -> String {
        let stats = await dataConsistencyService.getDataStatisticsSummary()

        return "验证完成：用户 \(stats.userCount) 个，成员 \(stats.memberCount) 个，积分记录 \(stats.pointRecordCount) 条"
    }

    /**
     * 完成恢复
     */
    private func finalizeRecovery() async {
        // 执行最终备份
        await multiLayerStorageManager.performFullBackup()
        _ = await pointRecordBackupService.performFullBackup()

        // 触发CloudKit同步
        _ = await enhancedCloudKitSyncService.performIntelligentSync()
    }

    /**
     * 删除所有Core Data实体
     */
    private func deleteAllCoreDataEntities() async throws {
        let context = coreDataManager.viewContext

        // 删除所有实体类型
        let entityNames = ["User", "Member", "PointRecord", "DiaryEntry", "RedemptionRecord", "LotteryRecord", "Subscription"]

        for entityName in entityNames {
            let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: entityName)
            let deleteRequest = NSBatchDeleteRequest(fetchRequest: fetchRequest)

            try context.execute(deleteRequest)
        }

        try context.save()
    }

    /**
     * 清除UserDefaults备份
     */
    private func clearUserDefaultsBackups() {
        let keys = [
            "local_point_records_backup",
            "local_members_backup",
            "local_user_data_backup",
            "local_last_sync_timestamp"
        ]

        for key in keys {
            UserDefaults.standard.removeObject(forKey: key)
        }
    }

    /**
     * 创建备份导出
     */
    private func createBackupExport() async throws -> Data {
        let stats = await dataConsistencyService.getDataStatisticsSummary()

        let exportData = BackupExportData(
            exportDate: Date(),
            appVersion: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown",
            dataStatistics: stats,
            pointRecordsBackupCount: pointRecordBackupService.totalBackupCount
        )

        return try JSONEncoder().encode(exportData)
    }

    /**
     * 从备份数据恢复
     */
    private func restoreFromBackupData(_ data: Data) async throws -> Bool {
        let exportData = try JSONDecoder().decode(BackupExportData.self, from: data)

        // 这里可以实现具体的恢复逻辑
        // 目前只是验证数据格式

        print("📊 备份信息：导出时间 \(exportData.exportDate)，应用版本 \(exportData.appVersion)")

        return true
    }
}

// MARK: - Data Structures

/**
 * 恢复进度
 */
struct RecoveryProgress {
    var currentStep: String = ""
    var progress: Double = 0.0
    var isCompleted: Bool = false

    var displayText: String {
        return "\(currentStep) (\(Int(progress * 100))%)"
    }
}

/**
 * 诊断报告
 */
struct DiagnosisReport {
    var diagnosisStartTime: Date = Date()
    var diagnosisEndTime: Date = Date()

    var dataIntegrityStatus: DataIntegrityStatus = .healthy("")
    var backupAvailability: BackupAvailabilityStatus = .unavailable("")
    var cloudKitStatus: CloudKitConnectionStatus = .disconnected("")
    var consistencyStatus: DataConsistencyStatus = .consistent("")

    var recommendations: [String] = []

    var diagnosisDuration: TimeInterval {
        return diagnosisEndTime.timeIntervalSince(diagnosisStartTime)
    }
}

/**
 * 恢复报告
 */
struct RecoveryReport {
    var success: Bool = false
    var message: String = ""
    var recoveryStartTime: Date = Date()
    var recoveryEndTime: Date = Date()

    var diagnosisReport: DiagnosisReport?
    var backupStatus: String = ""
    var recoveryResult: String = ""
    var validationResult: String = ""

    var recoveryDuration: TimeInterval {
        return recoveryEndTime.timeIntervalSince(recoveryStartTime)
    }
}

/**
 * 备份信息
 */
struct BackupInfo {
    let type: BackupType
    let source: String
    let timestamp: Date
    let isValid: Bool
    let description: String
}

/**
 * 备份类型
 */
enum BackupType {
    case pointRecords
    case multiLayer
    case cloudKit
}

/**
 * 数据完整性状态
 */
enum DataIntegrityStatus {
    case healthy(String)
    case corrupted(String)
    case empty(String)

    var message: String {
        switch self {
        case .healthy(let msg), .corrupted(let msg), .empty(let msg):
            return msg
        }
    }

    var isHealthy: Bool {
        switch self {
        case .healthy:
            return true
        default:
            return false
        }
    }
}

/**
 * 备份可用性状态
 */
enum BackupAvailabilityStatus {
    case available(String)
    case partial(String)
    case unavailable(String)

    var message: String {
        switch self {
        case .available(let msg), .partial(let msg), .unavailable(let msg):
            return msg
        }
    }

    var isAvailable: Bool {
        switch self {
        case .available, .partial:
            return true
        case .unavailable:
            return false
        }
    }
}

/**
 * CloudKit连接状态
 */
enum CloudKitConnectionStatus {
    case connected(String)
    case disconnected(String)

    var message: String {
        switch self {
        case .connected(let msg), .disconnected(let msg):
            return msg
        }
    }

    var isConnected: Bool {
        switch self {
        case .connected:
            return true
        case .disconnected:
            return false
        }
    }
}

/**
 * 数据一致性状态
 */
enum DataConsistencyStatus {
    case consistent(String)
    case inconsistent(String)

    var message: String {
        switch self {
        case .consistent(let msg), .inconsistent(let msg):
            return msg
        }
    }

    var isConsistent: Bool {
        switch self {
        case .consistent:
            return true
        case .inconsistent:
            return false
        }
    }
}

/**
 * 备份导出数据
 */
struct BackupExportData: Codable {
    let exportDate: Date
    let appVersion: String
    let dataStatistics: DataStatistics
    let pointRecordsBackupCount: Int
}
