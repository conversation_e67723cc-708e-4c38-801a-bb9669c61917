//
//  CloudKitSyncMonitor.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import Foundation
import CoreData
import CloudKit
import Combine

/**
 * CloudKit同步监控器
 * 实时监控同步状态、性能指标和健康状况
 */
@MainActor
class CloudKitSyncMonitor: ObservableObject {
    
    // MARK: - Singleton
    static let shared = CloudKitSyncMonitor()
    
    // MARK: - Published Properties
    @Published var syncMetrics: SyncMetrics = SyncMetrics()
    @Published var healthStatus: SyncHealthStatus = .unknown
    @Published var performanceMetrics: PerformanceMetrics = PerformanceMetrics()
    @Published var recentSyncEvents: [SyncEvent] = []
    
    // MARK: - Private Properties
    private let enhancedSyncService = EnhancedCloudKitSyncService.shared
    private let errorHandler = CloudKitErrorHandler.shared
    private var cancellables = Set<AnyCancellable>()
    private var syncStartTime: Date?
    private var eventHistory: [SyncEvent] = []
    
    // 监控配置
    private let maxEventHistory = 100
    private let healthCheckInterval: TimeInterval = 300 // 5分钟
    
    // MARK: - Initialization
    private init() {
        setupMonitoring()
        startHealthCheck()
    }
    
    // MARK: - Public Methods
    
    /**
     * 记录同步开始事件
     */
    func recordSyncStart() {
        syncStartTime = Date()
        
        let event = SyncEvent(
            type: .syncStarted,
            timestamp: Date(),
            message: "开始CloudKit同步",
            metadata: [:]
        )
        
        addSyncEvent(event)
        updateSyncMetrics()
    }
    
    /**
     * 记录同步成功事件
     */
    func recordSyncSuccess(duration: TimeInterval? = nil) {
        let actualDuration = duration ?? calculateSyncDuration()
        
        let event = SyncEvent(
            type: .syncCompleted,
            timestamp: Date(),
            message: "CloudKit同步成功完成",
            metadata: ["duration": actualDuration]
        )
        
        addSyncEvent(event)
        updateSyncMetrics()
        updatePerformanceMetrics(duration: actualDuration)
        
        syncStartTime = nil
    }
    
    /**
     * 记录同步失败事件
     */
    func recordSyncFailure(error: Error, retryCount: Int = 0) {
        let analysis = errorHandler.analyzeError(error)
        let duration = calculateSyncDuration()
        
        let event = SyncEvent(
            type: .syncFailed,
            timestamp: Date(),
            message: "CloudKit同步失败: \(analysis.userMessage)",
            metadata: [
                "error_type": analysis.type,
                "severity": analysis.severity,
                "retry_count": retryCount,
                "duration": duration
            ]
        )
        
        addSyncEvent(event)
        updateSyncMetrics()
        
        syncStartTime = nil
    }
    
    /**
     * 记录重试事件
     */
    func recordRetryAttempt(retryCount: Int, delay: TimeInterval) {
        let event = SyncEvent(
            type: .retryAttempt,
            timestamp: Date(),
            message: "第\(retryCount)次重试，延迟\(Int(delay))秒",
            metadata: [
                "retry_count": retryCount,
                "delay": delay
            ]
        )
        
        addSyncEvent(event)
    }
    
    /**
     * 记录网络状态变更
     */
    func recordNetworkStatusChange(status: NetworkStatus) {
        let event = SyncEvent(
            type: .networkStatusChanged,
            timestamp: Date(),
            message: "网络状态变更: \(status)",
            metadata: ["network_status": status]
        )
        
        addSyncEvent(event)
    }
    
    /**
     * 获取同步健康报告
     */
    func generateHealthReport() -> SyncHealthReport {
        let recentEvents = getRecentEvents(hours: 24)
        let successRate = calculateSuccessRate(events: recentEvents)
        let averageDuration = calculateAverageDuration(events: recentEvents)
        let errorFrequency = calculateErrorFrequency(events: recentEvents)
        
        return SyncHealthReport(
            healthStatus: healthStatus,
            successRate: successRate,
            averageSyncDuration: averageDuration,
            errorFrequency: errorFrequency,
            totalSyncs: syncMetrics.totalSyncs,
            recentErrors: getRecentErrors(hours: 24),
            recommendations: generateRecommendations()
        )
    }
    
    /**
     * 清除历史数据
     */
    func clearHistory() {
        eventHistory.removeAll()
        recentSyncEvents.removeAll()
        syncMetrics = SyncMetrics()
        performanceMetrics = PerformanceMetrics()
        
        print("🧹 同步监控历史数据已清除")
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置监控
     */
    private func setupMonitoring() {
        // 监听同步状态变更
        enhancedSyncService.$syncStatus
            .sink { [weak self] status in
                self?.handleSyncStatusChange(status)
            }
            .store(in: &cancellables)
        
        // 监听网络状态变更
        enhancedSyncService.$networkStatus
            .sink { [weak self] status in
                self?.recordNetworkStatusChange(status: status)
            }
            .store(in: &cancellables)
    }
    
    /**
     * 开始健康检查
     */
    private func startHealthCheck() {
        Timer.scheduledTimer(withTimeInterval: healthCheckInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.performHealthCheck()
            }
        }
    }
    
    /**
     * 执行健康检查
     */
    private func performHealthCheck() {
        let recentEvents = getRecentEvents(hours: 1)
        let successRate = calculateSuccessRate(events: recentEvents)
        let errorCount = recentEvents.filter { $0.type == .syncFailed }.count
        
        // 评估健康状态
        if successRate >= 0.9 && errorCount == 0 {
            healthStatus = .healthy
        } else if successRate >= 0.7 && errorCount <= 2 {
            healthStatus = .warning
        } else {
            healthStatus = .critical
        }
        
        print("🏥 同步健康检查完成: \(healthStatus)")
    }
    
    /**
     * 处理同步状态变更
     */
    private func handleSyncStatusChange(_ status: EnhancedSyncStatus) {
        switch status {
        case .syncing:
            recordSyncStart()
        case .completed:
            recordSyncSuccess()
        case .failed(let error):
            recordSyncFailure(error: error)
        case .retrying(let count, let delay):
            recordRetryAttempt(retryCount: count, delay: delay)
        default:
            break
        }
    }
    
    /**
     * 添加同步事件
     */
    private func addSyncEvent(_ event: SyncEvent) {
        eventHistory.append(event)
        
        // 限制历史记录数量
        if eventHistory.count > maxEventHistory {
            eventHistory.removeFirst(eventHistory.count - maxEventHistory)
        }
        
        // 更新最近事件（最多显示20个）
        recentSyncEvents = Array(eventHistory.suffix(20).reversed())
    }
    
    /**
     * 更新同步指标
     */
    private func updateSyncMetrics() {
        let recentEvents = getRecentEvents(hours: 24)
        
        syncMetrics.totalSyncs = eventHistory.filter { $0.type == .syncStarted }.count
        syncMetrics.successfulSyncs = eventHistory.filter { $0.type == .syncCompleted }.count
        syncMetrics.failedSyncs = eventHistory.filter { $0.type == .syncFailed }.count
        syncMetrics.successRate = calculateSuccessRate(events: recentEvents)
        syncMetrics.lastSyncDate = eventHistory.last?.timestamp
    }
    
    /**
     * 更新性能指标
     */
    private func updatePerformanceMetrics(duration: TimeInterval) {
        performanceMetrics.lastSyncDuration = duration
        
        let durations = getRecentSyncDurations(hours: 24)
        if !durations.isEmpty {
            performanceMetrics.averageSyncDuration = durations.reduce(0, +) / Double(durations.count)
            performanceMetrics.minSyncDuration = durations.min() ?? 0
            performanceMetrics.maxSyncDuration = durations.max() ?? 0
        }
    }
    
    /**
     * 计算同步持续时间
     */
    private func calculateSyncDuration() -> TimeInterval {
        guard let startTime = syncStartTime else { return 0 }
        return Date().timeIntervalSince(startTime)
    }
    
    /**
     * 获取最近事件
     */
    private func getRecentEvents(hours: Int) -> [SyncEvent] {
        let cutoffDate = Date().addingTimeInterval(-TimeInterval(hours * 3600))
        return eventHistory.filter { $0.timestamp >= cutoffDate }
    }
    
    /**
     * 计算成功率
     */
    private func calculateSuccessRate(events: [SyncEvent]) -> Double {
        let syncEvents = events.filter { $0.type == .syncStarted || $0.type == .syncCompleted || $0.type == .syncFailed }
        let successEvents = events.filter { $0.type == .syncCompleted }
        
        guard syncEvents.count > 0 else { return 1.0 }
        return Double(successEvents.count) / Double(syncEvents.count)
    }
    
    /**
     * 计算平均持续时间
     */
    private func calculateAverageDuration(events: [SyncEvent]) -> TimeInterval {
        let durations = events.compactMap { event -> TimeInterval? in
            guard let duration = event.metadata["duration"] as? TimeInterval else { return nil }
            return duration
        }
        
        guard !durations.isEmpty else { return 0 }
        return durations.reduce(0, +) / Double(durations.count)
    }
    
    /**
     * 计算错误频率
     */
    private func calculateErrorFrequency(events: [SyncEvent]) -> Double {
        let errorEvents = events.filter { $0.type == .syncFailed }
        let totalHours = 24.0
        return Double(errorEvents.count) / totalHours
    }
    
    /**
     * 获取最近同步持续时间
     */
    private func getRecentSyncDurations(hours: Int) -> [TimeInterval] {
        let recentEvents = getRecentEvents(hours: hours)
        return recentEvents.compactMap { event -> TimeInterval? in
            guard let duration = event.metadata["duration"] as? TimeInterval else { return nil }
            return duration
        }
    }
    
    /**
     * 获取最近错误
     */
    private func getRecentErrors(hours: Int) -> [SyncEvent] {
        let recentEvents = getRecentEvents(hours: hours)
        return recentEvents.filter { $0.type == .syncFailed }
    }
    
    /**
     * 生成建议
     */
    private func generateRecommendations() -> [String] {
        var recommendations: [String] = []
        
        if syncMetrics.successRate < 0.8 {
            recommendations.append("同步成功率较低，建议检查网络连接和iCloud设置")
        }
        
        if performanceMetrics.averageSyncDuration > 30 {
            recommendations.append("同步时间较长，建议清理旧数据或检查网络速度")
        }
        
        let recentErrors = getRecentErrors(hours: 24)
        if recentErrors.count > 5 {
            recommendations.append("最近错误频率较高，建议联系技术支持")
        }
        
        if recommendations.isEmpty {
            recommendations.append("同步状态良好，无需特别处理")
        }
        
        return recommendations
    }
}

// MARK: - Data Structures

/**
 * 同步事件
 */
struct SyncEvent {
    let id = UUID()
    let type: SyncEventType
    let timestamp: Date
    let message: String
    let metadata: [String: Any]
}

/**
 * 同步事件类型
 */
enum SyncEventType {
    case syncStarted
    case syncCompleted
    case syncFailed
    case retryAttempt
    case networkStatusChanged
    case accountStatusChanged
}

/**
 * 同步指标
 */
struct SyncMetrics {
    var totalSyncs: Int = 0
    var successfulSyncs: Int = 0
    var failedSyncs: Int = 0
    var successRate: Double = 1.0
    var lastSyncDate: Date?
}

/**
 * 性能指标
 */
struct PerformanceMetrics {
    var lastSyncDuration: TimeInterval = 0
    var averageSyncDuration: TimeInterval = 0
    var minSyncDuration: TimeInterval = 0
    var maxSyncDuration: TimeInterval = 0
}

/**
 * 同步健康状态
 */
enum SyncHealthStatus {
    case unknown
    case healthy
    case warning
    case critical

    var displayText: String {
        switch self {
        case .unknown:
            return "未知"
        case .healthy:
            return "健康"
        case .warning:
            return "警告"
        case .critical:
            return "严重"
        }
    }

    var color: String {
        switch self {
        case .unknown:
            return "gray"
        case .healthy:
            return "green"
        case .warning:
            return "orange"
        case .critical:
            return "red"
        }
    }
}

/**
 * 同步健康报告
 */
struct SyncHealthReport {
    let healthStatus: SyncHealthStatus
    let successRate: Double
    let averageSyncDuration: TimeInterval
    let errorFrequency: Double
    let totalSyncs: Int
    let recentErrors: [SyncEvent]
    let recommendations: [String]
}
