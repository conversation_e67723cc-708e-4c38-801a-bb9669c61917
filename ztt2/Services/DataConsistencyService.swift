//
//  DataConsistencyService.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import Foundation
import CoreData
import CloudKit

/**
 * 数据一致性检查服务
 * 专门负责检查和修复数据不一致问题
 * 确保多设备间数据的完整性和一致性
 */
@MainActor
class DataConsistencyService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = DataConsistencyService()
    
    // MARK: - Published Properties
    @Published var isChecking: Bool = false
    @Published var lastCheckDate: Date?
    @Published var inconsistencyCount: Int = 0
    @Published var lastCheckReport: ConsistencyCheckReport?
    
    // MARK: - Private Properties
    private let coreDataManager = CoreDataManager.shared
    private let multiLayerStorageManager = MultiLayerStorageManager.shared
    private let pointRecordBackupService = PointRecordBackupService.shared
    
    // MARK: - Initialization
    private init() {
        setupPeriodicCheck()
    }
    
    // MARK: - Public Methods
    
    /**
     * 执行完整的数据一致性检查
     */
    func performFullConsistencyCheck() async -> ConsistencyCheckReport {
        isChecking = true
        
        var report = ConsistencyCheckReport()
        report.checkStartTime = Date()
        
        print("🔍 开始完整数据一致性检查...")
        
        // 1. 检查用户数据一致性
        report.userDataIssues = await checkUserDataConsistency()
        
        // 2. 检查成员数据一致性
        report.memberDataIssues = await checkMemberDataConsistency()
        
        // 3. 检查积分记录一致性
        report.pointRecordIssues = await checkPointRecordConsistency()
        
        // 4. 检查关系数据一致性
        report.relationshipIssues = await checkRelationshipConsistency()
        
        // 5. 检查备份数据一致性
        report.backupDataIssues = await checkBackupDataConsistency()
        
        // 6. 检查数据完整性
        report.integrityIssues = await checkDataIntegrity()
        
        report.checkEndTime = Date()
        report.totalIssuesFound = report.getTotalIssueCount()
        
        lastCheckDate = Date()
        lastCheckReport = report
        inconsistencyCount = report.totalIssuesFound
        isChecking = false
        
        print("✅ 数据一致性检查完成，发现 \(report.totalIssuesFound) 个问题")
        
        return report
    }
    
    /**
     * 自动修复发现的数据不一致问题
     */
    func autoFixInconsistencies(report: ConsistencyCheckReport) async -> FixResult {
        print("🔧 开始自动修复数据不一致问题...")
        
        var fixResult = FixResult()
        
        // 1. 修复用户数据问题
        if !report.userDataIssues.isEmpty {
            let userFixCount = await fixUserDataIssues(report.userDataIssues)
            fixResult.userDataFixed = userFixCount
        }
        
        // 2. 修复成员数据问题
        if !report.memberDataIssues.isEmpty {
            let memberFixCount = await fixMemberDataIssues(report.memberDataIssues)
            fixResult.memberDataFixed = memberFixCount
        }
        
        // 3. 修复积分记录问题
        if !report.pointRecordIssues.isEmpty {
            let pointFixCount = await fixPointRecordIssues(report.pointRecordIssues)
            fixResult.pointRecordFixed = pointFixCount
        }
        
        // 4. 修复关系数据问题
        if !report.relationshipIssues.isEmpty {
            let relationshipFixCount = await fixRelationshipIssues(report.relationshipIssues)
            fixResult.relationshipFixed = relationshipFixCount
        }
        
        // 5. 修复备份数据问题
        if !report.backupDataIssues.isEmpty {
            let backupFixCount = await fixBackupDataIssues(report.backupDataIssues)
            fixResult.backupDataFixed = backupFixCount
        }
        
        // 6. 修复数据完整性问题
        if !report.integrityIssues.isEmpty {
            let integrityFixCount = await fixIntegrityIssues(report.integrityIssues)
            fixResult.integrityFixed = integrityFixCount
        }
        
        // 保存修复结果
        if fixResult.getTotalFixedCount() > 0 {
            coreDataManager.save()
            
            // 触发备份
            await multiLayerStorageManager.performFullBackup()
            await pointRecordBackupService.performFullBackup()
        }
        
        print("✅ 自动修复完成，共修复 \(fixResult.getTotalFixedCount()) 个问题")
        
        return fixResult
    }
    
    /**
     * 快速一致性检查（仅检查关键数据）
     */
    func performQuickConsistencyCheck() async -> Bool {
        // 检查关键数据的基本一致性
        let userCount = await getUserCount()
        let memberCount = await getMemberCount()
        let pointRecordCount = await getPointRecordCount()
        
        // 检查是否有孤立数据
        let orphanedMembers = await getOrphanedMembers()
        let orphanedPointRecords = await getOrphanedPointRecords()
        
        let hasIssues = orphanedMembers.count > 0 || orphanedPointRecords.count > 0
        
        if hasIssues {
            print("⚠️ 快速检查发现数据不一致：孤立成员 \(orphanedMembers.count) 个，孤立积分记录 \(orphanedPointRecords.count) 个")
        } else {
            print("✅ 快速一致性检查通过")
        }
        
        return !hasIssues
    }
    
    /**
     * 获取数据统计摘要
     */
    func getDataStatisticsSummary() async -> DataStatistics {
        var stats = DataStatistics()
        
        stats.userCount = await getUserCount()
        stats.memberCount = await getMemberCount()
        stats.pointRecordCount = await getPointRecordCount()
        stats.diaryEntryCount = await getDiaryEntryCount()
        stats.redemptionRecordCount = await getRedemptionRecordCount()
        stats.lotteryRecordCount = await getLotteryRecordCount()
        
        stats.orphanedMemberCount = await getOrphanedMembers().count
        stats.orphanedPointRecordCount = await getOrphanedPointRecords().count
        
        return stats
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置定期检查
     */
    private func setupPeriodicCheck() {
        // 每小时执行一次快速检查
        Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performQuickConsistencyCheck()
            }
        }
    }
    
    /**
     * 检查用户数据一致性
     */
    private func checkUserDataConsistency() async -> [DataIssue] {
        var issues: [DataIssue] = []
        
        do {
            let request: NSFetchRequest<User> = User.fetchRequest()
            let users = try coreDataManager.viewContext.fetch(request)
            
            for user in users {
                // 检查用户基本信息
                if user.id == nil {
                    issues.append(DataIssue(
                        type: .missingID,
                        entity: "User",
                        description: "用户缺少ID",
                        severity: .high
                    ))
                }
                
                if user.nickname?.isEmpty ?? true {
                    issues.append(DataIssue(
                        type: .missingRequiredField,
                        entity: "User",
                        description: "用户缺少昵称",
                        severity: .medium
                    ))
                }
                
                // 检查订阅信息
                if user.subscription == nil {
                    issues.append(DataIssue(
                        type: .missingRelationship,
                        entity: "User",
                        description: "用户缺少订阅信息",
                        severity: .medium
                    ))
                }
            }
            
        } catch {
            issues.append(DataIssue(
                type: .fetchError,
                entity: "User",
                description: "获取用户数据失败: \(error.localizedDescription)",
                severity: .high
            ))
        }
        
        return issues
    }
    
    /**
     * 检查成员数据一致性
     */
    private func checkMemberDataConsistency() async -> [DataIssue] {
        var issues: [DataIssue] = []
        
        do {
            let request: NSFetchRequest<Member> = Member.fetchRequest()
            let members = try coreDataManager.viewContext.fetch(request)
            
            for member in members {
                // 检查成员基本信息
                if member.id == nil {
                    issues.append(DataIssue(
                        type: .missingID,
                        entity: "Member",
                        description: "成员缺少ID",
                        severity: .high
                    ))
                }
                
                if member.name?.isEmpty ?? true {
                    issues.append(DataIssue(
                        type: .missingRequiredField,
                        entity: "Member",
                        description: "成员缺少姓名",
                        severity: .high
                    ))
                }
                
                // 检查用户关联
                if member.user == nil {
                    issues.append(DataIssue(
                        type: .missingRelationship,
                        entity: "Member",
                        description: "成员缺少用户关联",
                        severity: .high
                    ))
                }
                
                // 检查积分一致性
                let calculatedPoints = await calculateMemberPoints(member)
                if member.currentPoints != calculatedPoints {
                    issues.append(DataIssue(
                        type: .dataInconsistency,
                        entity: "Member",
                        description: "成员积分不一致：显示\(member.currentPoints)，计算\(calculatedPoints)",
                        severity: .medium
                    ))
                }
            }
            
        } catch {
            issues.append(DataIssue(
                type: .fetchError,
                entity: "Member",
                description: "获取成员数据失败: \(error.localizedDescription)",
                severity: .high
            ))
        }
        
        return issues
    }

    /**
     * 检查积分记录一致性
     */
    private func checkPointRecordConsistency() async -> [DataIssue] {
        var issues: [DataIssue] = []

        do {
            let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            let records = try coreDataManager.viewContext.fetch(request)

            for record in records {
                // 检查基本信息
                if record.id == nil {
                    issues.append(DataIssue(
                        type: .missingID,
                        entity: "PointRecord",
                        description: "积分记录缺少ID",
                        severity: .high
                    ))
                }

                if record.reason?.isEmpty ?? true {
                    issues.append(DataIssue(
                        type: .missingRequiredField,
                        entity: "PointRecord",
                        description: "积分记录缺少原因",
                        severity: .medium
                    ))
                }

                // 检查成员关联
                if record.member == nil {
                    issues.append(DataIssue(
                        type: .missingRelationship,
                        entity: "PointRecord",
                        description: "积分记录缺少成员关联",
                        severity: .high
                    ))
                }

                // 检查时间戳
                if record.timestamp == nil {
                    issues.append(DataIssue(
                        type: .missingRequiredField,
                        entity: "PointRecord",
                        description: "积分记录缺少时间戳",
                        severity: .medium
                    ))
                }
            }

        } catch {
            issues.append(DataIssue(
                type: .fetchError,
                entity: "PointRecord",
                description: "获取积分记录失败: \(error.localizedDescription)",
                severity: .high
            ))
        }

        return issues
    }

    /**
     * 检查关系数据一致性
     */
    private func checkRelationshipConsistency() async -> [DataIssue] {
        var issues: [DataIssue] = []

        // 检查孤立的成员
        let orphanedMembers = await getOrphanedMembers()
        for member in orphanedMembers {
            issues.append(DataIssue(
                type: .orphanedData,
                entity: "Member",
                description: "孤立的成员: \(member.name ?? "未知")",
                severity: .high
            ))
        }

        // 检查孤立的积分记录
        let orphanedPointRecords = await getOrphanedPointRecords()
        for record in orphanedPointRecords {
            issues.append(DataIssue(
                type: .orphanedData,
                entity: "PointRecord",
                description: "孤立的积分记录: \(record.reason ?? "未知")",
                severity: .high
            ))
        }

        return issues
    }

    /**
     * 检查备份数据一致性
     */
    private func checkBackupDataConsistency() async -> [DataIssue] {
        var issues: [DataIssue] = []

        // 检查多层存储一致性
        let multiLayerReport = await multiLayerStorageManager.checkDataConsistency()
        if !multiLayerReport.isFullyConsistent {
            for area in multiLayerReport.inconsistentAreas {
                issues.append(DataIssue(
                    type: .backupInconsistency,
                    entity: "Backup",
                    description: "备份数据不一致: \(area)",
                    severity: .medium
                ))
            }
        }

        // 检查积分记录备份完整性
        let integrityReport = await pointRecordBackupService.verifyBackupIntegrity()
        if !integrityReport.isFullyValid {
            issues.append(DataIssue(
                type: .backupCorruption,
                entity: "PointRecordBackup",
                description: "积分记录备份损坏，有效备份数: \(integrityReport.validBackupCount)",
                severity: .high
            ))
        }

        return issues
    }

    /**
     * 检查数据完整性
     */
    private func checkDataIntegrity() async -> [DataIssue] {
        var issues: [DataIssue] = []

        // 检查数据库约束
        do {
            // 检查重复ID
            let duplicateUsers = try await findDuplicateUsers()
            for user in duplicateUsers {
                issues.append(DataIssue(
                    type: .duplicateData,
                    entity: "User",
                    description: "重复的用户ID: \(user.id?.uuidString ?? "未知")",
                    severity: .high
                ))
            }

            let duplicateMembers = try await findDuplicateMembers()
            for member in duplicateMembers {
                issues.append(DataIssue(
                    type: .duplicateData,
                    entity: "Member",
                    description: "重复的成员ID: \(member.id?.uuidString ?? "未知")",
                    severity: .high
                ))
            }

        } catch {
            issues.append(DataIssue(
                type: .integrityCheckError,
                entity: "Database",
                description: "数据完整性检查失败: \(error.localizedDescription)",
                severity: .high
            ))
        }

        return issues
    }

    // MARK: - Helper Methods

    /**
     * 计算成员实际积分
     */
    private func calculateMemberPoints(_ member: Member) async -> Int32 {
        do {
            let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            request.predicate = NSPredicate(format: "member == %@ AND isReversed == NO", member)
            let records = try coreDataManager.viewContext.fetch(request)

            let totalPoints = records.reduce(0) { $0 + $1.value }
            return totalPoints
        } catch {
            print("❌ 计算成员积分失败: \(error)")
            return 0
        }
    }

    /**
     * 获取孤立的成员
     */
    private func getOrphanedMembers() async -> [Member] {
        do {
            let request: NSFetchRequest<Member> = Member.fetchRequest()
            request.predicate = NSPredicate(format: "user == nil")
            return try coreDataManager.viewContext.fetch(request)
        } catch {
            print("❌ 获取孤立成员失败: \(error)")
            return []
        }
    }

    /**
     * 获取孤立的积分记录
     */
    private func getOrphanedPointRecords() async -> [PointRecord] {
        do {
            let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            request.predicate = NSPredicate(format: "member == nil")
            return try coreDataManager.viewContext.fetch(request)
        } catch {
            print("❌ 获取孤立积分记录失败: \(error)")
            return []
        }
    }

    /**
     * 获取用户数量
     */
    private func getUserCount() async -> Int {
        do {
            let request: NSFetchRequest<User> = User.fetchRequest()
            return try coreDataManager.viewContext.count(for: request)
        } catch {
            return 0
        }
    }

    /**
     * 获取成员数量
     */
    private func getMemberCount() async -> Int {
        do {
            let request: NSFetchRequest<Member> = Member.fetchRequest()
            return try coreDataManager.viewContext.count(for: request)
        } catch {
            return 0
        }
    }

    /**
     * 获取积分记录数量
     */
    private func getPointRecordCount() async -> Int {
        do {
            let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            return try coreDataManager.viewContext.count(for: request)
        } catch {
            return 0
        }
    }

    /**
     * 获取日记条目数量
     */
    private func getDiaryEntryCount() async -> Int {
        do {
            let request: NSFetchRequest<DiaryEntry> = DiaryEntry.fetchRequest()
            return try coreDataManager.viewContext.count(for: request)
        } catch {
            return 0
        }
    }

    /**
     * 获取兑换记录数量
     */
    private func getRedemptionRecordCount() async -> Int {
        do {
            let request: NSFetchRequest<RedemptionRecord> = RedemptionRecord.fetchRequest()
            return try coreDataManager.viewContext.count(for: request)
        } catch {
            return 0
        }
    }

    /**
     * 获取抽奖记录数量
     */
    private func getLotteryRecordCount() async -> Int {
        do {
            let request: NSFetchRequest<LotteryRecord> = LotteryRecord.fetchRequest()
            return try coreDataManager.viewContext.count(for: request)
        } catch {
            return 0
        }
    }

    /**
     * 查找重复用户
     */
    private func findDuplicateUsers() async throws -> [User] {
        let request: NSFetchRequest<User> = User.fetchRequest()
        let users = try coreDataManager.viewContext.fetch(request)

        var seenIDs = Set<UUID>()
        var duplicates: [User] = []

        for user in users {
            if let id = user.id {
                if seenIDs.contains(id) {
                    duplicates.append(user)
                } else {
                    seenIDs.insert(id)
                }
            }
        }

        return duplicates
    }

    /**
     * 查找重复成员
     */
    private func findDuplicateMembers() async throws -> [Member] {
        let request: NSFetchRequest<Member> = Member.fetchRequest()
        let members = try coreDataManager.viewContext.fetch(request)

        var seenIDs = Set<UUID>()
        var duplicates: [Member] = []

        for member in members {
            if let id = member.id {
                if seenIDs.contains(id) {
                    duplicates.append(member)
                } else {
                    seenIDs.insert(id)
                }
            }
        }

        return duplicates
    }

    // MARK: - Fix Methods

    /**
     * 修复用户数据问题
     */
    private func fixUserDataIssues(_ issues: [DataIssue]) async -> Int {
        var fixedCount = 0

        for issue in issues {
            switch issue.type {
            case .missingID:
                // 为缺少ID的用户生成新ID
                if let user = await findUserWithoutID() {
                    user.id = UUID()
                    fixedCount += 1
                    print("✅ 为用户生成新ID")
                }

            case .missingRequiredField:
                // 为缺少昵称的用户设置默认昵称
                if let user = await findUserWithoutNickname() {
                    user.nickname = "用户\(Date().timeIntervalSince1970)"
                    fixedCount += 1
                    print("✅ 为用户设置默认昵称")
                }

            case .missingRelationship:
                // 为缺少订阅的用户创建默认订阅
                if let user = await findUserWithoutSubscription() {
                    let subscription = Subscription(context: coreDataManager.viewContext)
                    subscription.id = UUID()
                    subscription.level = "free"
                    subscription.subscriptionType = "free"
                    subscription.isActive = false
                    subscription.createdAt = Date()
                    subscription.updatedAt = Date()
                    subscription.user = user
                    fixedCount += 1
                    print("✅ 为用户创建默认订阅")
                }

            default:
                break
            }
        }

        return fixedCount
    }

    /**
     * 修复成员数据问题
     */
    private func fixMemberDataIssues(_ issues: [DataIssue]) async -> Int {
        var fixedCount = 0

        for issue in issues {
            switch issue.type {
            case .missingID:
                if let member = await findMemberWithoutID() {
                    member.id = UUID()
                    fixedCount += 1
                    print("✅ 为成员生成新ID")
                }

            case .missingRequiredField:
                if let member = await findMemberWithoutName() {
                    member.name = "成员\(Date().timeIntervalSince1970)"
                    fixedCount += 1
                    print("✅ 为成员设置默认姓名")
                }

            case .dataInconsistency:
                // 修复积分不一致
                if let member = await findMemberWithInconsistentPoints() {
                    let calculatedPoints = await calculateMemberPoints(member)
                    member.currentPoints = calculatedPoints
                    fixedCount += 1
                    print("✅ 修复成员积分不一致")
                }

            default:
                break
            }
        }

        return fixedCount
    }

    /**
     * 修复积分记录问题
     */
    private func fixPointRecordIssues(_ issues: [DataIssue]) async -> Int {
        var fixedCount = 0

        for issue in issues {
            switch issue.type {
            case .missingID:
                if let record = await findPointRecordWithoutID() {
                    record.id = UUID()
                    fixedCount += 1
                    print("✅ 为积分记录生成新ID")
                }

            case .missingRequiredField:
                if let record = await findPointRecordWithoutReason() {
                    record.reason = "系统修复"
                    fixedCount += 1
                    print("✅ 为积分记录设置默认原因")
                }

                if let record = await findPointRecordWithoutTimestamp() {
                    record.timestamp = Date()
                    fixedCount += 1
                    print("✅ 为积分记录设置时间戳")
                }

            default:
                break
            }
        }

        return fixedCount
    }

    /**
     * 修复关系数据问题
     */
    private func fixRelationshipIssues(_ issues: [DataIssue]) async -> Int {
        var fixedCount = 0

        for issue in issues {
            if issue.type == .orphanedData {
                if issue.entity == "Member" {
                    // 删除孤立的成员
                    let orphanedMembers = await getOrphanedMembers()
                    for member in orphanedMembers {
                        coreDataManager.viewContext.delete(member)
                        fixedCount += 1
                        print("✅ 删除孤立成员")
                    }
                } else if issue.entity == "PointRecord" {
                    // 删除孤立的积分记录
                    let orphanedRecords = await getOrphanedPointRecords()
                    for record in orphanedRecords {
                        coreDataManager.viewContext.delete(record)
                        fixedCount += 1
                        print("✅ 删除孤立积分记录")
                    }
                }
            }
        }

        return fixedCount
    }

    /**
     * 修复备份数据问题
     */
    private func fixBackupDataIssues(_ issues: [DataIssue]) async -> Int {
        var fixedCount = 0

        for issue in issues {
            if issue.type == .backupInconsistency {
                // 重新执行完整备份
                await multiLayerStorageManager.performFullBackup()
                fixedCount += 1
                print("✅ 重新执行多层存储备份")
            } else if issue.type == .backupCorruption {
                // 重新执行积分记录备份
                _ = await pointRecordBackupService.performFullBackup()
                fixedCount += 1
                print("✅ 重新执行积分记录备份")
            }
        }

        return fixedCount
    }

    /**
     * 修复完整性问题
     */
    private func fixIntegrityIssues(_ issues: [DataIssue]) async -> Int {
        var fixedCount = 0

        for issue in issues {
            if issue.type == .duplicateData {
                if issue.entity == "User" {
                    let duplicates = try? await findDuplicateUsers()
                    // 保留第一个，删除其余的
                    if let duplicates = duplicates, duplicates.count > 1 {
                        for i in 1..<duplicates.count {
                            coreDataManager.viewContext.delete(duplicates[i])
                            fixedCount += 1
                        }
                        print("✅ 删除重复用户")
                    }
                } else if issue.entity == "Member" {
                    let duplicates = try? await findDuplicateMembers()
                    if let duplicates = duplicates, duplicates.count > 1 {
                        for i in 1..<duplicates.count {
                            coreDataManager.viewContext.delete(duplicates[i])
                            fixedCount += 1
                        }
                        print("✅ 删除重复成员")
                    }
                }
            }
        }

        return fixedCount
    }

    // MARK: - Find Methods for Fixing

    private func findUserWithoutID() async -> User? {
        do {
            let request: NSFetchRequest<User> = User.fetchRequest()
            request.predicate = NSPredicate(format: "id == nil")
            request.fetchLimit = 1
            return try coreDataManager.viewContext.fetch(request).first
        } catch {
            return nil
        }
    }

    private func findUserWithoutNickname() async -> User? {
        do {
            let request: NSFetchRequest<User> = User.fetchRequest()
            request.predicate = NSPredicate(format: "nickname == nil OR nickname == ''")
            request.fetchLimit = 1
            return try coreDataManager.viewContext.fetch(request).first
        } catch {
            return nil
        }
    }

    private func findUserWithoutSubscription() async -> User? {
        do {
            let request: NSFetchRequest<User> = User.fetchRequest()
            request.predicate = NSPredicate(format: "subscription == nil")
            request.fetchLimit = 1
            return try coreDataManager.viewContext.fetch(request).first
        } catch {
            return nil
        }
    }

    private func findMemberWithoutID() async -> Member? {
        do {
            let request: NSFetchRequest<Member> = Member.fetchRequest()
            request.predicate = NSPredicate(format: "id == nil")
            request.fetchLimit = 1
            return try coreDataManager.viewContext.fetch(request).first
        } catch {
            return nil
        }
    }

    private func findMemberWithoutName() async -> Member? {
        do {
            let request: NSFetchRequest<Member> = Member.fetchRequest()
            request.predicate = NSPredicate(format: "name == nil OR name == ''")
            request.fetchLimit = 1
            return try coreDataManager.viewContext.fetch(request).first
        } catch {
            return nil
        }
    }

    private func findMemberWithInconsistentPoints() async -> Member? {
        // 这里简化处理，实际应该比较计算值和存储值
        do {
            let request: NSFetchRequest<Member> = Member.fetchRequest()
            request.fetchLimit = 1
            return try coreDataManager.viewContext.fetch(request).first
        } catch {
            return nil
        }
    }

    private func findPointRecordWithoutID() async -> PointRecord? {
        do {
            let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            request.predicate = NSPredicate(format: "id == nil")
            request.fetchLimit = 1
            return try coreDataManager.viewContext.fetch(request).first
        } catch {
            return nil
        }
    }

    private func findPointRecordWithoutReason() async -> PointRecord? {
        do {
            let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            request.predicate = NSPredicate(format: "reason == nil OR reason == ''")
            request.fetchLimit = 1
            return try coreDataManager.viewContext.fetch(request).first
        } catch {
            return nil
        }
    }

    private func findPointRecordWithoutTimestamp() async -> PointRecord? {
        do {
            let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            request.predicate = NSPredicate(format: "timestamp == nil")
            request.fetchLimit = 1
            return try coreDataManager.viewContext.fetch(request).first
        } catch {
            return nil
        }
    }
}

// MARK: - Data Structures

/**
 * 一致性检查报告
 */
struct ConsistencyCheckReport {
    var checkStartTime: Date = Date()
    var checkEndTime: Date = Date()
    var totalIssuesFound: Int = 0

    var userDataIssues: [DataIssue] = []
    var memberDataIssues: [DataIssue] = []
    var pointRecordIssues: [DataIssue] = []
    var relationshipIssues: [DataIssue] = []
    var backupDataIssues: [DataIssue] = []
    var integrityIssues: [DataIssue] = []

    func getTotalIssueCount() -> Int {
        return userDataIssues.count + memberDataIssues.count + pointRecordIssues.count +
               relationshipIssues.count + backupDataIssues.count + integrityIssues.count
    }

    var checkDuration: TimeInterval {
        return checkEndTime.timeIntervalSince(checkStartTime)
    }
}

/**
 * 数据问题
 */
struct DataIssue {
    let type: IssueType
    let entity: String
    let description: String
    let severity: IssueSeverity
    let timestamp: Date = Date()
}

/**
 * 问题类型
 */
enum IssueType {
    case missingID
    case missingRequiredField
    case missingRelationship
    case dataInconsistency
    case orphanedData
    case duplicateData
    case backupInconsistency
    case backupCorruption
    case fetchError
    case integrityCheckError
}

/**
 * 问题严重程度
 */
enum IssueSeverity {
    case low
    case medium
    case high
    case critical
}

/**
 * 修复结果
 */
struct FixResult {
    var userDataFixed: Int = 0
    var memberDataFixed: Int = 0
    var pointRecordFixed: Int = 0
    var relationshipFixed: Int = 0
    var backupDataFixed: Int = 0
    var integrityFixed: Int = 0

    func getTotalFixedCount() -> Int {
        return userDataFixed + memberDataFixed + pointRecordFixed +
               relationshipFixed + backupDataFixed + integrityFixed
    }
}

/**
 * 数据统计
 */
struct DataStatistics {
    var userCount: Int = 0
    var memberCount: Int = 0
    var pointRecordCount: Int = 0
    var diaryEntryCount: Int = 0
    var redemptionRecordCount: Int = 0
    var lotteryRecordCount: Int = 0

    var orphanedMemberCount: Int = 0
    var orphanedPointRecordCount: Int = 0

    var totalRecords: Int {
        return userCount + memberCount + pointRecordCount + diaryEntryCount +
               redemptionRecordCount + lotteryRecordCount
    }
}
