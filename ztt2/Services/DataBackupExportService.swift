//
//  DataBackupExportService.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import Foundation
import CoreData
import UniformTypeIdentifiers

/**
 * 数据备份导出服务
 * 提供数据导出和导入功能，支持多种格式
 */
@MainActor
class DataBackupExportService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = DataBackupExportService()
    
    // MARK: - Published Properties
    @Published var isExporting: Bool = false
    @Published var isImporting: Bool = false
    @Published var exportProgress: Double = 0.0
    @Published var importProgress: Double = 0.0
    
    // MARK: - Private Properties
    private let coreDataManager = CoreDataManager.shared
    private let pointRecordBackupService = PointRecordBackupService.shared
    
    // MARK: - Initialization
    private init() {}
    
    // MARK: - Public Methods
    
    /**
     * 导出完整数据备份
     */
    func exportFullBackup() async -> ExportResult {
        guard !isExporting else {
            return ExportResult(success: false, message: "导出正在进行中", filePath: nil)
        }
        
        isExporting = true
        exportProgress = 0.0
        
        print("📤 开始导出完整数据备份...")
        
        do {
            // 1. 收集用户数据 (20%)
            exportProgress = 0.2
            let users = try await fetchUsers()
            
            // 2. 收集成员数据 (40%)
            exportProgress = 0.4
            let members = try await fetchMembers()
            
            // 3. 收集积分记录 (60%)
            exportProgress = 0.6
            let pointRecords = try await fetchPointRecords()
            
            // 4. 收集其他数据 (80%)
            exportProgress = 0.8
            let diaryEntries = try await fetchDiaryEntries()
            let redemptionRecords = try await fetchRedemptionRecords()
            let lotteryRecords = try await fetchLotteryRecords()
            
            // 5. 创建备份文件 (100%)
            exportProgress = 1.0
            let backupData = FullBackupData(
                exportDate: Date(),
                appVersion: getAppVersion(),
                users: users.map { createUserExport(from: $0) },
                members: members.map { createMemberExport(from: $0) },
                pointRecords: pointRecords.map { createPointRecordExport(from: $0) },
                diaryEntries: diaryEntries.map { createDiaryEntryExport(from: $0) },
                redemptionRecords: redemptionRecords.map { createRedemptionRecordExport(from: $0) },
                lotteryRecords: lotteryRecords.map { createLotteryRecordExport(from: $0) }
            )
            
            let filePath = try await saveBackupToFile(backupData)
            
            isExporting = false
            print("✅ 完整数据备份导出成功: \(filePath)")
            
            return ExportResult(
                success: true,
                message: "导出成功，包含 \(users.count) 个用户，\(members.count) 个成员，\(pointRecords.count) 条积分记录",
                filePath: filePath
            )
            
        } catch {
            isExporting = false
            print("❌ 数据备份导出失败: \(error)")
            
            return ExportResult(
                success: false,
                message: "导出失败: \(error.localizedDescription)",
                filePath: nil
            )
        }
    }
    
    /**
     * 导出积分记录备份
     */
    func exportPointRecordsOnly() async -> ExportResult {
        print("📊 导出积分记录备份...")
        
        do {
            let pointRecords = try await fetchPointRecords()
            
            let backupData = PointRecordsBackupData(
                exportDate: Date(),
                appVersion: getAppVersion(),
                pointRecords: pointRecords.map { createPointRecordExport(from: $0) }
            )
            
            let filePath = try await savePointRecordsToFile(backupData)
            
            print("✅ 积分记录备份导出成功")
            
            return ExportResult(
                success: true,
                message: "导出成功，包含 \(pointRecords.count) 条积分记录",
                filePath: filePath
            )
            
        } catch {
            print("❌ 积分记录备份导出失败: \(error)")
            
            return ExportResult(
                success: false,
                message: "导出失败: \(error.localizedDescription)",
                filePath: nil
            )
        }
    }
    
    /**
     * 从文件导入数据
     */
    func importBackupFromFile(_ url: URL) async -> ImportResult {
        guard !isImporting else {
            return ImportResult(success: false, message: "导入正在进行中")
        }
        
        isImporting = true
        importProgress = 0.0
        
        print("📥 从文件导入数据备份...")
        
        do {
            // 1. 读取文件 (20%)
            importProgress = 0.2
            let data = try Data(contentsOf: url)
            
            // 2. 解析数据 (40%)
            importProgress = 0.4
            let backupData = try JSONDecoder().decode(FullBackupData.self, from: data)
            
            // 3. 验证数据 (60%)
            importProgress = 0.6
            try validateBackupData(backupData)
            
            // 4. 导入数据 (80%)
            importProgress = 0.8
            let importStats = try await importBackupData(backupData)
            
            // 5. 完成导入 (100%)
            importProgress = 1.0
            await finalizeImport()
            
            isImporting = false
            print("✅ 数据备份导入成功")
            
            return ImportResult(
                success: true,
                message: "导入成功：\(importStats.description)"
            )
            
        } catch {
            isImporting = false
            print("❌ 数据备份导入失败: \(error)")
            
            return ImportResult(
                success: false,
                message: "导入失败: \(error.localizedDescription)"
            )
        }
    }
    
    /**
     * 获取支持的文件类型
     */
    func getSupportedFileTypes() -> [UTType] {
        return [UTType.json, UTType.data]
    }
    
    /**
     * 生成备份文件名
     */
    func generateBackupFileName(type: BackupFileType = .full) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd_HHmmss"
        let timestamp = formatter.string(from: Date())
        
        switch type {
        case .full:
            return "ztt2_full_backup_\(timestamp).json"
        case .pointRecords:
            return "ztt2_points_backup_\(timestamp).json"
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 获取应用版本
     */
    private func getAppVersion() -> String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
    }
    
    /**
     * 获取用户数据
     */
    private func fetchUsers() async throws -> [User] {
        let request: NSFetchRequest<User> = User.fetchRequest()
        return try coreDataManager.viewContext.fetch(request)
    }
    
    /**
     * 获取成员数据
     */
    private func fetchMembers() async throws -> [Member] {
        let request: NSFetchRequest<Member> = Member.fetchRequest()
        return try coreDataManager.viewContext.fetch(request)
    }
    
    /**
     * 获取积分记录
     */
    private func fetchPointRecords() async throws -> [PointRecord] {
        let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        request.sortDescriptors = [NSSortDescriptor(keyPath: \PointRecord.timestamp, ascending: false)]
        return try coreDataManager.viewContext.fetch(request)
    }
    
    /**
     * 获取日记条目
     */
    private func fetchDiaryEntries() async throws -> [DiaryEntry] {
        let request: NSFetchRequest<DiaryEntry> = DiaryEntry.fetchRequest()
        return try coreDataManager.viewContext.fetch(request)
    }
    
    /**
     * 获取兑换记录
     */
    private func fetchRedemptionRecords() async throws -> [RedemptionRecord] {
        let request: NSFetchRequest<RedemptionRecord> = RedemptionRecord.fetchRequest()
        return try coreDataManager.viewContext.fetch(request)
    }
    
    /**
     * 获取抽奖记录
     */
    private func fetchLotteryRecords() async throws -> [LotteryRecord] {
        let request: NSFetchRequest<LotteryRecord> = LotteryRecord.fetchRequest()
        return try coreDataManager.viewContext.fetch(request)
    }
    
    /**
     * 创建用户导出数据
     */
    private func createUserExport(from user: User) -> UserExportData {
        return UserExportData(
            id: user.id?.uuidString ?? "",
            nickname: user.nickname ?? "",
            email: user.email ?? "",
            appleUserID: user.appleUserID ?? "",
            subscriptionType: user.subscriptionType ?? "free",
            createdAt: user.createdAt ?? Date()
        )
    }
    
    /**
     * 创建成员导出数据
     */
    private func createMemberExport(from member: Member) -> MemberExportData {
        return MemberExportData(
            id: member.id?.uuidString ?? "",
            name: member.name ?? "",
            role: member.role ?? "",
            birthDate: member.birthDate,
            memberNumber: member.memberNumber,
            currentPoints: member.currentPoints,
            avatar: member.avatar,
            createdAt: member.createdAt ?? Date(),
            updatedAt: member.updatedAt ?? Date(),
            userID: member.user?.id?.uuidString ?? ""
        )
    }
    
    /**
     * 创建积分记录导出数据
     */
    private func createPointRecordExport(from record: PointRecord) -> PointRecordExportData {
        return PointRecordExportData(
            id: record.id?.uuidString ?? "",
            reason: record.reason ?? "",
            value: record.value,
            timestamp: record.timestamp ?? Date(),
            recordType: record.recordType ?? "behavior",
            isReversed: record.isReversed,
            memberID: record.member?.id?.uuidString ?? ""
        )
    }

    /**
     * 创建日记条目导出数据
     */
    private func createDiaryEntryExport(from entry: DiaryEntry) -> DiaryEntryExportData {
        return DiaryEntryExportData(
            id: entry.id?.uuidString ?? "",
            content: entry.content ?? "",
            timestamp: entry.timestamp ?? Date(),
            memberID: entry.member?.id?.uuidString ?? ""
        )
    }

    /**
     * 创建兑换记录导出数据
     */
    private func createRedemptionRecordExport(from record: RedemptionRecord) -> RedemptionRecordExportData {
        return RedemptionRecordExportData(
            id: record.id?.uuidString ?? "",
            rewardName: record.rewardName ?? "",
            pointsCost: record.pointsCost,
            timestamp: record.timestamp ?? Date(),
            memberID: record.member?.id?.uuidString ?? ""
        )
    }

    /**
     * 创建抽奖记录导出数据
     */
    private func createLotteryRecordExport(from record: LotteryRecord) -> LotteryRecordExportData {
        return LotteryRecordExportData(
            id: record.id?.uuidString ?? "",
            result: record.result ?? "",
            pointsCost: record.pointsCost,
            timestamp: record.timestamp ?? Date(),
            memberID: record.member?.id?.uuidString ?? ""
        )
    }

    /**
     * 保存备份到文件
     */
    private func saveBackupToFile(_ backupData: FullBackupData) async throws -> String {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = .prettyPrinted

        let data = try encoder.encode(backupData)

        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileName = generateBackupFileName(type: .full)
        let fileURL = documentsPath.appendingPathComponent(fileName)

        try data.write(to: fileURL)

        return fileURL.path
    }

    /**
     * 保存积分记录到文件
     */
    private func savePointRecordsToFile(_ backupData: PointRecordsBackupData) async throws -> String {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = .prettyPrinted

        let data = try encoder.encode(backupData)

        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileName = generateBackupFileName(type: .pointRecords)
        let fileURL = documentsPath.appendingPathComponent(fileName)

        try data.write(to: fileURL)

        return fileURL.path
    }

    /**
     * 验证备份数据
     */
    private func validateBackupData(_ backupData: FullBackupData) throws {
        // 检查数据完整性
        if backupData.users.isEmpty && backupData.members.isEmpty && backupData.pointRecords.isEmpty {
            throw BackupError.emptyBackup
        }

        // 检查版本兼容性
        let currentVersion = getAppVersion()
        if !isVersionCompatible(backupVersion: backupData.appVersion, currentVersion: currentVersion) {
            throw BackupError.incompatibleVersion(backupData.appVersion, currentVersion)
        }

        // 检查数据关联性
        let userIDs = Set(backupData.users.map { $0.id })
        let memberUserIDs = Set(backupData.members.map { $0.userID })

        if !memberUserIDs.isSubset(of: userIDs) {
            throw BackupError.invalidDataRelationship("成员数据中存在无效的用户关联")
        }
    }

    /**
     * 检查版本兼容性
     */
    private func isVersionCompatible(backupVersion: String, currentVersion: String) -> Bool {
        // 简化的版本兼容性检查
        // 实际项目中可以实现更复杂的版本比较逻辑
        return true
    }

    /**
     * 导入备份数据
     */
    private func importBackupData(_ backupData: FullBackupData) async throws -> ImportStatistics {
        var stats = ImportStatistics()

        let context = coreDataManager.viewContext

        // 导入用户
        for userData in backupData.users {
            if !userExists(id: userData.id) {
                let user = User(context: context)
                user.id = UUID(uuidString: userData.id) ?? UUID()
                user.nickname = userData.nickname
                user.email = userData.email
                user.appleUserID = userData.appleUserID
                user.subscriptionType = userData.subscriptionType
                user.createdAt = userData.createdAt
                stats.usersImported += 1
            }
        }

        // 导入成员
        for memberData in backupData.members {
            if !memberExists(id: memberData.id) {
                let member = Member(context: context)
                member.id = UUID(uuidString: memberData.id) ?? UUID()
                member.name = memberData.name
                member.role = memberData.role
                member.birthDate = memberData.birthDate
                member.memberNumber = memberData.memberNumber
                member.currentPoints = memberData.currentPoints
                member.avatar = memberData.avatar
                member.createdAt = memberData.createdAt
                member.updatedAt = memberData.updatedAt

                // 关联用户
                if let userUUID = UUID(uuidString: memberData.userID) {
                    let userRequest: NSFetchRequest<User> = User.fetchRequest()
                    userRequest.predicate = NSPredicate(format: "id == %@", userUUID as CVarArg)
                    if let user = try context.fetch(userRequest).first {
                        member.user = user
                    }
                }

                stats.membersImported += 1
            }
        }

        // 导入积分记录
        for recordData in backupData.pointRecords {
            if !pointRecordExists(id: recordData.id) {
                let record = PointRecord(context: context)
                record.id = UUID(uuidString: recordData.id) ?? UUID()
                record.reason = recordData.reason
                record.value = recordData.value
                record.timestamp = recordData.timestamp
                record.recordType = recordData.recordType
                record.isReversed = recordData.isReversed

                // 关联成员
                if let memberUUID = UUID(uuidString: recordData.memberID) {
                    let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
                    memberRequest.predicate = NSPredicate(format: "id == %@", memberUUID as CVarArg)
                    if let member = try context.fetch(memberRequest).first {
                        record.member = member
                    }
                }

                stats.pointRecordsImported += 1
            }
        }

        // 保存数据
        try context.save()

        return stats
    }

    /**
     * 完成导入
     */
    private func finalizeImport() async {
        // 触发备份
        await pointRecordBackupService.performFullBackup()

        // 刷新UI
        coreDataManager.viewContext.refreshAllObjects()
    }

    /**
     * 检查用户是否存在
     */
    private func userExists(id: String) -> Bool {
        guard let uuid = UUID(uuidString: id) else { return false }

        let request: NSFetchRequest<User> = User.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", uuid as CVarArg)
        request.fetchLimit = 1

        do {
            let count = try coreDataManager.viewContext.count(for: request)
            return count > 0
        } catch {
            return false
        }
    }

    /**
     * 检查成员是否存在
     */
    private func memberExists(id: String) -> Bool {
        guard let uuid = UUID(uuidString: id) else { return false }

        let request: NSFetchRequest<Member> = Member.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", uuid as CVarArg)
        request.fetchLimit = 1

        do {
            let count = try coreDataManager.viewContext.count(for: request)
            return count > 0
        } catch {
            return false
        }
    }

    /**
     * 检查积分记录是否存在
     */
    private func pointRecordExists(id: String) -> Bool {
        guard let uuid = UUID(uuidString: id) else { return false }

        let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", uuid as CVarArg)
        request.fetchLimit = 1

        do {
            let count = try coreDataManager.viewContext.count(for: request)
            return count > 0
        } catch {
            return false
        }
    }
}

// MARK: - Data Structures

/**
 * 导出结果
 */
struct ExportResult {
    let success: Bool
    let message: String
    let filePath: String?
}

/**
 * 导入结果
 */
struct ImportResult {
    let success: Bool
    let message: String
}

/**
 * 导入统计
 */
struct ImportStatistics {
    var usersImported: Int = 0
    var membersImported: Int = 0
    var pointRecordsImported: Int = 0
    var diaryEntriesImported: Int = 0
    var redemptionRecordsImported: Int = 0
    var lotteryRecordsImported: Int = 0

    var description: String {
        return "用户 \(usersImported) 个，成员 \(membersImported) 个，积分记录 \(pointRecordsImported) 条"
    }
}

/**
 * 备份文件类型
 */
enum BackupFileType {
    case full
    case pointRecords
}

/**
 * 备份错误
 */
enum BackupError: LocalizedError {
    case emptyBackup
    case incompatibleVersion(String, String)
    case invalidDataRelationship(String)
    case fileNotFound
    case corruptedData

    var errorDescription: String? {
        switch self {
        case .emptyBackup:
            return "备份文件为空"
        case .incompatibleVersion(let backupVersion, let currentVersion):
            return "版本不兼容：备份版本 \(backupVersion)，当前版本 \(currentVersion)"
        case .invalidDataRelationship(let message):
            return "数据关联错误：\(message)"
        case .fileNotFound:
            return "备份文件未找到"
        case .corruptedData:
            return "备份数据已损坏"
        }
    }
}

/**
 * 完整备份数据
 */
struct FullBackupData: Codable {
    let exportDate: Date
    let appVersion: String
    let users: [UserExportData]
    let members: [MemberExportData]
    let pointRecords: [PointRecordExportData]
    let diaryEntries: [DiaryEntryExportData]
    let redemptionRecords: [RedemptionRecordExportData]
    let lotteryRecords: [LotteryRecordExportData]
}

/**
 * 积分记录备份数据
 */
struct PointRecordsBackupData: Codable {
    let exportDate: Date
    let appVersion: String
    let pointRecords: [PointRecordExportData]
}

/**
 * 用户导出数据
 */
struct UserExportData: Codable {
    let id: String
    let nickname: String
    let email: String
    let appleUserID: String
    let subscriptionType: String
    let createdAt: Date
}

/**
 * 成员导出数据
 */
struct MemberExportData: Codable {
    let id: String
    let name: String
    let role: String
    let birthDate: Date?
    let memberNumber: Int32
    let currentPoints: Int32
    let avatar: String?
    let createdAt: Date
    let updatedAt: Date
    let userID: String
}

/**
 * 积分记录导出数据
 */
struct PointRecordExportData: Codable {
    let id: String
    let reason: String
    let value: Int32
    let timestamp: Date
    let recordType: String
    let isReversed: Bool
    let memberID: String
}

/**
 * 日记条目导出数据
 */
struct DiaryEntryExportData: Codable {
    let id: String
    let content: String
    let timestamp: Date
    let memberID: String
}

/**
 * 兑换记录导出数据
 */
struct RedemptionRecordExportData: Codable {
    let id: String
    let rewardName: String
    let pointsCost: Int32
    let timestamp: Date
    let memberID: String
}

/**
 * 抽奖记录导出数据
 */
struct LotteryRecordExportData: Codable {
    let id: String
    let result: String
    let pointsCost: Int32
    let timestamp: Date
    let memberID: String
}
