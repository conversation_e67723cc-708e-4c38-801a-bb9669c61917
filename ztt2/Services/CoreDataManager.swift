//
//  CoreDataManager.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/3.
//

import Foundation
import CoreData
import CloudKit
import SwiftUI
import Combine

/**
 * Core Data管理器
 * 负责CloudKit同步状态监控和数据一致性管理
 * 支持多层数据存储架构：CoreData+CloudKit、NSUbiquitousKeyValueStore、UserDefaults
 */
@MainActor
class CoreDataManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = CoreDataManager()
    
    // MARK: - Published Properties
    @Published var cloudKitStatus: CloudKitSyncStatus = .notStarted
    @Published var isSyncing: Bool = false
    @Published var lastSyncDate: Date?
    @Published var syncError: Error?
    
    // MARK: - Private Properties
    private let persistenceController = PersistenceController.shared
    private let enhancedSyncService = EnhancedCloudKitSyncService.shared
    private let syncMonitor = CloudKitSyncMonitor.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Computed Properties
    
    /**
     * 获取视图上下文
     */
    var viewContext: NSManagedObjectContext {
        return persistenceController.container.viewContext
    }
    
    /**
     * 获取CloudKit同步状态
     */
    var cloudKitSyncEnabled: Bool {
        return persistenceController.isCloudKitEnabled
    }
    
    // MARK: - Initialization
    
    private init() {
        setupCloudKitNotifications()
        setupInitialState()
    }
    
    // MARK: - Setup Methods
    
    /**
     * 设置CloudKit通知监听
     */
    private func setupCloudKitNotifications() {
        // 监听CloudKit导入通知
        NotificationCenter.default.addObserver(
            forName: .NSPersistentStoreRemoteChange,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleCloudKitImport()
            }
        }
        
        // 监听CloudKit导出通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("NSPersistentStoreDidImportUbiquitousContentChanges"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleCloudKitExport()
            }
        }
    }
    
    /**
     * 设置初始状态
     */
    private func setupInitialState() {
        cloudKitStatus = .syncCompleted
        lastSyncDate = UserDefaults.standard.object(forKey: "last_cloudkit_sync_date") as? Date
        
        print("☁️ CoreDataManager初始化完成，CloudKit同步已启用")
    }
    
    // MARK: - CloudKit Sync Handlers
    
    /**
     * 处理CloudKit导入通知
     */
    private func handleCloudKitImport() {
        print("📥 CloudKit数据导入中...")
        
        cloudKitStatus = .syncInProgress
        isSyncing = true
        
        viewContext.perform {
            self.viewContext.refreshAllObjects()
            
            // 执行数据一致性检查和修复
            self.performDataConsistencyCheck()
            
            DispatchQueue.main.async {
                self.cloudKitStatus = .syncCompleted
                self.isSyncing = false
                self.lastSyncDate = Date()
                self.updateLastSyncDate()
                print("✅ CloudKit数据导入完成")
                
                // 发送同步完成通知
                NotificationCenter.default.post(
                    name: NSNotification.Name("CloudKitSyncCompleted"),
                    object: nil
                )
            }
        }
    }
    
    /**
     * 处理CloudKit导出通知
     */
    private func handleCloudKitExport() {
        print("📤 CloudKit数据导出中...")
        
        cloudKitStatus = .syncInProgress
        isSyncing = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.cloudKitStatus = .syncCompleted
            self.isSyncing = false
            self.lastSyncDate = Date()
            self.updateLastSyncDate()
            print("✅ CloudKit数据导出完成")
        }
    }
    
    // MARK: - Data Management
    
    /**
     * 保存上下文
     */
    func save() {
        persistenceController.save()
    }
    
    /**
     * 获取当前用户
     */
    func getCurrentUser() -> User? {
        return persistenceController.getCurrentUser()
    }
    
    /**
     * 手动触发CloudKit同步
     */
    func triggerCloudKitSync() {
        guard cloudKitSyncEnabled else {
            print("⚠️ CloudKit同步未启用")
            return
        }

        print("🔄 手动触发增强CloudKit同步...")

        cloudKitStatus = .syncInProgress
        isSyncing = true

        // 使用增强的同步服务
        Task {
            let result = await enhancedSyncService.performIntelligentSync()

            DispatchQueue.main.async {
                if result.success {
                    self.cloudKitStatus = .syncCompleted
                    self.isSyncing = false
                    self.lastSyncDate = Date()
                    self.updateLastSyncDate()
                    print("✅ 增强CloudKit同步成功完成")

                    // 刷新所有对象以获取最新的CloudKit数据
                    self.viewContext.refreshAllObjects()

                    // 发送同步完成通知
                    NotificationCenter.default.post(
                        name: NSNotification.Name("CloudKitSyncCompleted"),
                        object: nil
                    )
                    NotificationCenter.default.post(
                        name: NSNotification.Name("EnhancedCloudKitSyncCompleted"),
                        object: nil
                    )
                } else {
                    self.cloudKitStatus = .error
                    self.isSyncing = false
                    print("❌ 增强CloudKit同步失败: \(result.error?.localizedDescription ?? "未知错误")")
                }
            }
        }
    }
    
    // MARK: - Data Consistency
    
    /**
     * 执行数据一致性检查和修复
     */
    private func performDataConsistencyCheck() {
        print("🔍 执行数据一致性检查...")
        
        // 检查用户数据完整性
        checkUserDataIntegrity()
        
        // 检查成员数据完整性
        checkMemberDataIntegrity()
        
        // 检查积分记录完整性
        checkPointRecordIntegrity()
        
        print("✅ 数据一致性检查完成")
    }
    
    /**
     * 检查用户数据完整性
     */
    private func checkUserDataIntegrity() {
        let request: NSFetchRequest<User> = User.fetchRequest()
        
        do {
            let users = try viewContext.fetch(request)
            for user in users {
                // 确保用户有订阅信息
                if user.subscription == nil {
                    print("⚠️ 用户 \(user.nickname ?? "未知") 缺少订阅信息，正在修复...")
                    // 创建默认免费订阅
                    let subscription = Subscription(context: viewContext)
                    subscription.id = UUID()
                    subscription.subscriptionType = "free"
                    subscription.isActive = true
                    subscription.createdAt = Date()
                    subscription.updatedAt = Date()
                    subscription.user = user
                }
            }
        } catch {
            print("❌ 检查用户数据完整性失败: \(error)")
        }
    }
    
    /**
     * 检查成员数据完整性
     */
    private func checkMemberDataIntegrity() {
        let request: NSFetchRequest<Member> = Member.fetchRequest()
        
        do {
            let members = try viewContext.fetch(request)
            for member in members {
                // 确保成员有用户关联
                if member.user == nil {
                    print("⚠️ 成员 \(member.name ?? "未知") 缺少用户关联")
                }
                
                // 确保成员有有效的积分值
                if member.currentPoints < 0 {
                    print("⚠️ 成员 \(member.name ?? "未知") 积分为负数，正在修复...")
                    member.currentPoints = 0
                }
            }
        } catch {
            print("❌ 检查成员数据完整性失败: \(error)")
        }
    }
    
    /**
     * 检查积分记录完整性
     */
    private func checkPointRecordIntegrity() {
        let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        
        do {
            let records = try viewContext.fetch(request)
            for record in records {
                // 确保积分记录有成员关联
                if record.member == nil {
                    print("⚠️ 发现孤立的积分记录，正在删除...")
                    viewContext.delete(record)
                }
            }
        } catch {
            print("❌ 检查积分记录完整性失败: \(error)")
        }
    }
    
    // MARK: - Helper Methods
    
    /**
     * 更新最后同步时间
     */
    private func updateLastSyncDate() {
        UserDefaults.standard.set(lastSyncDate, forKey: "last_cloudkit_sync_date")
    }

    // MARK: - Reset Methods

    /**
     * 重置同步状态
     * 用于清除所有数据后的状态重置
     */
    func resetSyncStatus() {
        print("🔄 重置CoreDataManager同步状态...")

        // 重置发布的属性
        cloudKitStatus = .notStarted
        isSyncing = false
        lastSyncDate = nil
        syncError = nil

        // 取消所有观察者
        cancellables.removeAll()

        // 重新设置通知观察者
        setupCloudKitNotifications()

        // 重新设置初始状态
        setupInitialState()

        print("✅ CoreDataManager同步状态重置完成")
    }
}

// MARK: - CloudKit Sync Status Enum

enum CloudKitSyncStatus {
    case notStarted
    case syncInProgress
    case syncCompleted
    case syncFailed
    
    var displayText: String {
        switch self {
        case .notStarted:
            return "未开始"
        case .syncInProgress:
            return "同步中"
        case .syncCompleted:
            return "同步完成"
        case .syncFailed:
            return "同步失败"
        }
    }
    
    var iconName: String {
        switch self {
        case .notStarted:
            return "icloud"
        case .syncInProgress:
            return "icloud.and.arrow.up"
        case .syncCompleted:
            return "icloud.and.arrow.up.fill"
        case .syncFailed:
            return "icloud.slash"
        }
    }
}
